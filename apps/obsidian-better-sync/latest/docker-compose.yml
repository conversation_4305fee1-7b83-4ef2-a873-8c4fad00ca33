services:
  better-sync:
    container_name: ${CONTAINER_NAME}
    restart: always
    ports:
      - "${PANEL_APP_PORT_HTTP}:9000"
      - "${PRIVATE_HTTP_LISTEN}:9001"
    volumes:
      - ${DATA_PATH}/storage:/better-sync/storage/
      - ${DATA_PATH}/config:/better-sync/config/
    image: haierkeys/obsidian-better-sync-service:latest
    labels:
      createdBy: "Apps"
    networks:
      - 1panel-network

networks:
  1panel-network:
    external: true

